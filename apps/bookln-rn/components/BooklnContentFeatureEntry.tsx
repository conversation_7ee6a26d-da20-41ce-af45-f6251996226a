import { JglXStack } from '@jgl/ui-v4';
import { Fragment } from 'react';
import {
  Extrapolation,
  type SharedValue,
  interpolate,
  useDerivedValue,
} from 'react-native-reanimated';
import { ScrollView } from 'tamagui';
import { useBooklnContentFeatureEntry } from '../hooks/useBooklnContentFeatureEntry';
import { BooklnContentFeatureEntryItem } from './BooklnContentFeatureEntryItem';
import { LearnToolsModal } from './LearnToolsModal';

// 动画完成的滚动距离阈值
const ANIMATION_FINISH_OFFSET = 80;

type Props = {
  scrollTop: SharedValue<number>;
};

/**
 * 书链内容功能入口
 */
export const BooklnContentFeatureEntry = (props: Props) => {
  const { scrollTop } = props;

  const { featureEntryList, learnToolsModalVisible, onLearnToolsModalDismiss } =
    useBooklnContentFeatureEntry();

  const animationProgress = useDerivedValue(() => {
    return interpolate(
      scrollTop.value,
      [0, ANIMATION_FINISH_OFFSET],
      [0, 1],
      // Extrapolate.CLAMP: 保证输出值不会超过 [0, 1] 的范围
      Extrapolation.CLAMP,
    );
  });

  return (
    <Fragment>
      <ScrollView
        contentContainerStyle={{ paddingHorizontal: 12, minWidth: '100%' }}
        className='flex-row py-4'
        bounces={false}
        horizontal
      >
        <JglXStack space={12} jglClassName='w-full'>
          {featureEntryList.map((item) => {
            return (
              <BooklnContentFeatureEntryItem
                key={item.id}
                item={item}
                progress={animationProgress}
              />
            );
          })}
        </JglXStack>
      </ScrollView>

      <LearnToolsModal
        visible={learnToolsModalVisible}
        onDismiss={onLearnToolsModalDismiss}
      />
    </Fragment>
  );
};
