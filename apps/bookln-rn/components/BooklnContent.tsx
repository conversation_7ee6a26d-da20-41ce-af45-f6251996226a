import type { JglAiQAComponentRef } from '@jgl/ai-qa-v2';
import { JglAiQAComponent } from '@jgl/ai-qa-v2';
import { useIsUuidUser } from '@jgl/biz-func';
import { ContentContainer } from '@jgl/components';
import { JglYStack } from '@jgl/ui-v4';
import { featureToggles } from '@jgl/utils';
import type { ISession } from '@yunti-private/basic-im';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import type { NativeScrollEvent, NativeSyntheticEvent } from 'react-native';
import { useSharedValue } from 'react-native-reanimated';
import { BooklnContentFeatureEntry } from './BooklnContentFeatureEntry';
import { MyScannedBooksFoldable } from './MyScannedBooksFoldable';

export type BooklnContentRef = {
  createNewChatSession: () => void;
  openChatSessionItem: (item: ISession) => void;
};

type Props = {
  offsetTop?: number;
};

/**
 * 书链内容
 */
export const BooklnContent = forwardRef<BooklnContentRef, Props>(
  (props, ref) => {
    const { offsetTop } = props;

    const messageListScrollTop = useSharedValue(0);

    useImperativeHandle(ref, () => ({
      openChatSessionItem: (item: ISession) => {
        jglAiQAComponentRef.current?.openChatSessionItem(item);
      },
      createNewChatSession: () => {
        jglAiQAComponentRef.current?.createNewChatSession();
      },
    }));

    const jglAiQAComponentRef = useRef<JglAiQAComponentRef>(null);

    const isUUID = useIsUuidUser();

    const renderHeader = useMemo(() => {
      return (
        <JglYStack w='full' space={6}>
          {featureToggles.booklnHomeFeatureEntryVisible() ? (
            <BooklnContentFeatureEntry scrollTop={messageListScrollTop} />
          ) : null}
          {!isUUID ? <MyScannedBooksFoldable isShowEmpty={false} /> : null}
          {/* <JglAiQAWelcome /> */}
        </JglYStack>
      );
    }, [isUUID, messageListScrollTop]);

    const handleMessageListScroll = useCallback(
      (e: NativeSyntheticEvent<NativeScrollEvent>) => {
        const { contentOffset } = e.nativeEvent;
        const { y: scrollTop } = contentOffset;
        messageListScrollTop.value = scrollTop;
      },
      [messageListScrollTop],
    );

    return (
      <ContentContainer
        containerClassName='flex-1 self-center'
        containerStyle={{
          paddingTop: offsetTop,
        }}
      >
        {renderHeader}

        <JglAiQAComponent
          ref={jglAiQAComponentRef}
          onMessageListScroll={handleMessageListScroll}
        />
      </ContentContainer>
    );
  },
);
