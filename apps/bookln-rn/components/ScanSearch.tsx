import {
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useNavigationBarHeight } from '@yunti-private/jgl-ui';
import { FlashMode } from 'expo-camera';
import { useCallback } from 'react';
import { Platform } from 'react-native';
import Animated from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ClipPath, Defs, Rect, Svg } from 'react-native-svg';
import { Image } from 'tamagui';
import { useScanSearch } from '../hooks/useScanSearch';
import { CameraScanner } from './CameraScanner';

/**
 * 扫码搜索
 */
export const ScanSearch = () => {
  const { top, bottom } = useSafeAreaInsets();
  const {
    width,
    height,
    centerX,
    centerY,
    boxSize,
    onScanResult,
    onPressBack,
    onPressAlbum,
    onPressLight,
    flashMode,
    cameraScannerRef,
    cameraEnabled,
    scanLineStyle,
    scanningImgHeight,
    onPressCancelReview,
    scanningResult,
    handleSelectResult,
  } = useScanSearch();

  const navigationBarHeight = useNavigationBarHeight();

  /**
   * 头部
   */
  const renderHeader = useCallback(() => {
    return (
      <JglTouchable
        onPress={onPressBack}
        position='absolute'
        top={top + 16}
        left={16}
        minHeight={0}
        minWidth={0}
      >
        <Image
          source={require('../assets/images/ic_circle_back.png')}
          width={32}
          height={32}
        />
      </JglTouchable>
    );
  }, [top, onPressBack]);

  /**
   * 底部工具栏
   */
  const renderBottomToolBar = useCallback(() => {
    return (
      <JglXStack
        position='absolute'
        bottom={bottom + 56}
        paddingHorizontal={24}
        paddingVertical={12}
        width={'100%'}
        justifyContent='space-between'
      >
        <JglTouchable onPress={onPressAlbum}>
          <Image
            source={require('../assets/images/ic_album.png')}
            width={44}
            height={44}
          />
        </JglTouchable>
        <JglTouchable onPress={onPressLight}>
          <Image
            source={
              flashMode === FlashMode.torch
                ? require('../assets/images/ic_light_on.png')
                : require('../assets/images/ic_light_off.png')
            }
            width={44}
            height={44}
          />
        </JglTouchable>
      </JglXStack>
    );
  }, [bottom, flashMode, onPressAlbum, onPressLight]);

  if (!cameraEnabled) {
    return null;
  }

  return (
    <JglYStack flex={1}>
      <CameraScanner
        ref={cameraScannerRef}
        onScanResult={onScanResult}
        flashMode={flashMode}
      >
        <Svg
          height='100%'
          width='100%'
          className='absolute bottom-0 left-0 right-0 top-0'
        >
          {!scanningResult && (
            <Defs>
              <ClipPath id='clip'>
                {/* 全屏矩形 */}
                <Rect x='0' y='0' width={width} height={height + 40} />
                {/* 中间镂空区域 */}
                <Rect
                  x={centerX - boxSize / 2}
                  y={centerY - boxSize / 2}
                  width={boxSize}
                  height={boxSize}
                  rx={16}
                  ry={16}
                />
              </ClipPath>
            </Defs>
          )}

          {/* 半透明覆盖层 */}
          {!scanningResult && (
            <Rect
              x='0'
              y='0'
              width={width}
              height={'100%'}
              fill='rgba(0,0,0,0.5)'
              clipPath='url(#clip)'
            />
          )}
        </Svg>
      </CameraScanner>
      {!scanningResult && (
        <JglText
          position='absolute'
          top={centerY - 32 - boxSize / 2}
          color='white'
          alignSelf='center'
        >
          扫描图书二维码
        </JglText>
      )}
      {!scanningResult && (
        <Animated.Image
          source={require('../assets/images/ic_scanning.png')}
          className={'absolute'}
          width={boxSize}
          height={scanningImgHeight}
          style={[
            scanLineStyle,
            {
              left: centerX - boxSize / 2,
              top: centerY - boxSize / 2,
            },
          ]}
        />
      )}
      {!scanningResult && renderHeader()}
      {!scanningResult && renderBottomToolBar()}
      {scanningResult && scanningResult.length > 0 && (
        <JglYStack
          position='absolute'
          zIndex={1000}
          top={0}
          left={0}
          right={0}
          bottom={0}
        >
          {scanningResult.map((result) => {
            const { cornerPoints, data } = result;

            let [
              rightTop = { x: 0, y: 0 },
              rightBottom = { x: 0, y: 0 },
              leftBottom = { x: 0, y: 0 },
              leftTop = { x: 0, y: 0 },
            ] = cornerPoints;
            if (Platform.OS === 'ios') {
              rightTop = cornerPoints[3] ?? { x: 0, y: 0 };
              rightBottom = cornerPoints[0] ?? { x: 0, y: 0 };
              leftBottom = cornerPoints[1] ?? { x: 0, y: 0 };
              leftTop = cornerPoints[2] ?? { x: 0, y: 0 };
            }

            // 计算中心点
            const points = [rightTop, rightBottom, leftBottom, leftTop].filter(
              Boolean,
            );

            let arrowCenterX = 0;
            let arrowCenterY = 0;
            if (points.length === 4) {
              arrowCenterX =
                (rightTop.x + rightBottom.x + leftBottom.x + leftTop.x) / 4;
              arrowCenterY =
                (rightTop.y + rightBottom.y + leftBottom.y + leftTop.y) / 4;
            }

            if (scanningResult.length === 1) {
              return (
                <JglView
                  position='absolute'
                  zIndex={200}
                  width={24}
                  borderWidth={2}
                  borderColor='white'
                  borderRadius={100}
                  height={24}
                  key={data}
                  backgroundColor='#4E76FF'
                  top={arrowCenterY - 12}
                  left={arrowCenterX - 12}
                />
              );
            }
            return (
              <JglTouchable
                key={data}
                color='white'
                position='absolute'
                zIndex={200}
                onPress={() => handleSelectResult(result)}
                top={arrowCenterY - 20}
                left={arrowCenterX - 20}
              >
                <Image
                  source={require('../assets/images/ic_qr_arrow.png')}
                  width={30}
                  height={30}
                />
              </JglTouchable>
            );
          })}
          {scanningResult.length > 1 && (
            <JglYStack
              flex={1}
              backgroundColor='rgba(0,0,0,0.5)'
              justifyContent='space-between'
            >
              <JglXStack height={navigationBarHeight} pt={top} px={20}>
                <JglTouchable onPress={onPressCancelReview}>
                  <JglText fontSize={16} color='white'>
                    取消
                  </JglText>
                </JglTouchable>
              </JglXStack>
              <JglXStack justifyContent='center' mb={100} alignItems='center'>
                <JglText fontSize={16} color='white'>
                  轻触小蓝点，打开页面
                </JglText>
              </JglXStack>
            </JglYStack>
          )}
        </JglYStack>
      )}
    </JglYStack>
  );
};
