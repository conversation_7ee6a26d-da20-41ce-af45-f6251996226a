import { JglText, JglTouchable, JglYStack } from '@jgl/ui-v4';
import LinearGradient from 'react-native-linear-gradient';
import { Image } from 'tamagui';
import type { FeatureEntryType } from '../hooks/useBooklnContentFeatureEntry';
import Animated, {
  interpolate,
  type SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';

// 纵向布局 (progress = 0)
const CONTAINER_WIDTH_COLUMN = 80;
const CONTAINER_HEIGHT_COLUMN = 100;
const IMAGE_SIZE_COLUMN = 44;

// 横向布局 (progress = 1)
const CONTAINER_WIDTH_ROW = 180;
const CONTAINER_HEIGHT_ROW = 60;
const IMAGE_SIZE_ROW = 20;

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

type BooklnContentFeatureEntryItemProps = {
  item: FeatureEntryType;
  progress: SharedValue<number>;
};

/**
 * @description 书链内容功能入口 - 动画item
 * <AUTHOR>
 * @date 2025-06-16
 */
export const BooklnContentFeatureEntryItem = (
  props: BooklnContentFeatureEntryItemProps,
) => {
  const { item, progress } = props;

  const animatedContainerStyle = useAnimatedStyle(() => {
    const width = interpolate(
      progress.value,
      [0, 1],
      [CONTAINER_WIDTH_COLUMN, CONTAINER_WIDTH_ROW],
    );
    const height = interpolate(
      progress.value,
      [0, 1],
      [CONTAINER_HEIGHT_COLUMN, CONTAINER_HEIGHT_ROW],
    );
    // 横向布局时，内部元素需要横向排列
    const flexDirection = progress.value > 0.5 ? 'row' : 'column';

    return {
      width,
      height,
      flexDirection,
    };
  });

  // 图片的动画样式
  const animatedImageStyle = useAnimatedStyle(() => {
    const size = interpolate(
      progress.value,
      [0, 1],
      [IMAGE_SIZE_COLUMN, IMAGE_SIZE_ROW],
    );
    return {
      width: size,
      height: size,
    };
  });

  // 文本容器的动画样式 (为了更好地控制间距)
  const animatedTextContainerStyle = useAnimatedStyle(() => {
    // 纵向时有 marginTop，横向时有 marginLeft
    const marginTop = interpolate(progress.value, [0, 1], [4, 0]);
    const marginLeft = interpolate(progress.value, [0, 1], [0, 12]);
    return {
      marginTop,
      marginLeft,
      // 横向时，让文本部分占据剩余空间并垂直居中
      flex: progress.value > 0.5 ? 1 : 0,
      justifyContent: 'center',
    };
  });

  return (
    <JglTouchable onPress={item.onPress} jglClassName='flex-1 min-w-[64px]'>
      <AnimatedLinearGradient
        className='w-full rounded-2xl py-3'
        colors={item.backgroundColor}
        style={animatedContainerStyle}
      >
        <JglYStack
          space={4}
          jglClassName='w-full flex-center'
          borderRadius={16}
        >
          <Animated.View style={animatedImageStyle}>
            <Image source={item.image} className='aspect-square w-full' />
          </Animated.View>

          <Animated.View style={animatedTextContainerStyle}>
            <JglText fontSize={12} color='#FAFAFB'>
              {item.title}
            </JglText>
          </Animated.View>
        </JglYStack>
      </AnimatedLinearGradient>
    </JglTouchable>
  );
};
