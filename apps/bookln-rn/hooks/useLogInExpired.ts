import { withLoginLogicVersion } from '@bookln/bookln-biz';
import {
  agreementStateAtom,
  ApiErrorCode,
  routerMap,
  updateUserInfo,
  useAppDispatch,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import type { FetchOptions, YTRequest, YTResponse } from '@yunti-private/net';
import { useEffect, useRef } from 'react';
import { Alert } from 'react-native';
import { useLogIn } from './useLogIn';
import { getDefaultStore } from 'jotai';

/**
 * 登录过期错误码包含
 * 1. 登录失效
 * 2. 用户在其他设备登录过
 * 3. 用户未登录
 * 4. 用户已注销
 */
// enum ExpiredErrorCodes {
//   LoginExpired = ApiErrorCode.LoginExpired,
//   UserChangedDevice = ApiErrorCode.UserChangedDevice,
//   NeedsLogin = ApiErrorCode.NeedsLogin,
//   UserUnregister = ApiErrorCode.UserUnregister,
// }

const ExpiredErrorCodes = [
  ApiErrorCode.LoginExpired,
  ApiErrorCode.UserChangedDevice,
  ApiErrorCode.NeedsLogin,
  ApiErrorCode.UserUnregister,
];

/**
 * 处理登录过期
 */
export const useLogInExpired = () => {
  const isShowingAlertRef = useRef<boolean>(false);
  const { logInUuid } = useLogIn();
  const dispatch = useAppDispatch();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const responseOld = (params: {
      request: YTRequest<unknown>;
      options?: FetchOptions;
      response: YTResponse<unknown>;
    }) => {
      const { response } = params;
      const { errorCode, msg } = response;
      const title = msg ?? '登录已过期';
      if (errorCode && ExpiredErrorCodes.includes(errorCode)) {
        switch (errorCode) {
          case ApiErrorCode.NeedsLogin:
          case ApiErrorCode.UserUnregister: {
            logInUuid();
            /* 当前用户信息无效或sessionId缺失 */
            break;
          }
          case ApiErrorCode.LoginExpired:
          case ApiErrorCode.UserChangedDevice: {
            if (isShowingAlertRef.current) {
              return;
            }

            logInUuid();
            isShowingAlertRef.current = true;

            Alert.alert(
              title,
              undefined,
              [
                {
                  text: '取消',
                  onPress: () => {
                    isShowingAlertRef.current = false;
                  },
                },
                {
                  text: '重新登录',
                  onPress: () => {
                    router.push(routerMap.logInModal);
                    isShowingAlertRef.current = false;
                  },
                },
              ],
              {
                onDismiss: () => {
                  isShowingAlertRef.current = false;
                },
              },
            );
            break;
          }
        }
      }
    };

    const responseNew = (params: {
      request: YTRequest<unknown>;
      options?: FetchOptions;
      response: YTResponse<unknown>;
    }) => {
      const { response } = params;
      const { errorCode, msg } = response;
      const title = msg ?? '登录已过期，请重新登录';
      if (errorCode && ExpiredErrorCodes.includes(errorCode)) {
        dispatch(updateUserInfo({}));
        getDefaultStore().set(agreementStateAtom, 'undetermined');
      }
    };

    const listenerId = container.net().addListener({
      onResponse: withLoginLogicVersion(responseNew, responseOld),
    });

    return () => {
      container.net().removeListener(listenerId);
    };
  }, [dispatch, isShowingAlertRef, logInUuid]);
};
