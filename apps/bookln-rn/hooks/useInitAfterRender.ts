import {
  useIMEvents,
  useIMFetchAgentConf,
  useIMFetchAudioConf,
  useIMFetchIntentRecognizeRobotConf,
  useIMFetchRobotConf,
  useIMInitEventBus,
  useIMLoginAndLogout,
} from '@jgl/ai-qa-v2';
import { agreementStateAtom, store, updateOpenInstallInfo, useChannel } from '@jgl/biz-func';
import { LoggerHelper } from '@jgl/logger';
import { useScreenTrack } from '@jgl/utils';
import { usePlatformConfig } from '@yunti-private/net-query-hooks';
import { useAtomValue } from 'jotai';
import { useEffect } from 'react';
import { ChannelHelper } from '../utils/ChannelHelper';
import { useNetworkConsoleLog } from '../utils/networkLogUtil';
import { useInitSdk } from './sdk/useInitSdk';
import { useChangeRoot } from './useChangeRoot';
import { useCheckAppUpgrade } from './useCheckAppUpgrade';
import { useInitStorageAtom } from './useInitStorageAtom';
import { useLoadAndRefreshUserInfo } from './useLoadAndRefreshUserInfo';
import { useLogInExpired } from './useLogInExpired';

/**
 * 在渲染UI之后的一些初始化工作
 */
export const useInitAfterRender = () => {
  const agreementState = useAtomValue(agreementStateAtom);
  // 初始化存储原子
  useInitStorageAtom();
  // 开启网络请求log
  useNetworkConsoleLog();
  // 平台配置
  usePlatformConfig({ enabled: agreementState === 'agreed' });
  // 检查app升级
  useCheckAppUpgrade({ isActive: true });
  // 屏幕跟踪
  useScreenTrack();
  // 加载用户信息
  useLoadAndRefreshUserInfo();
  // 检查登录是否过期
  useLogInExpired();
  // 切换根路由
  useChangeRoot();
  // 初始化三方sdk
  useInitSdk();
  // 初始化IM登录和登出
  useIMLoginAndLogout();
  // 初始化IM事件监听
  useIMEvents();
  // 初始化智能体配置
  useIMFetchAgentConf();
  // 初始化 IM EventBus
  useIMInitEventBus();
  // 初始化IM机器人ID
  useIMFetchRobotConf();
  // 初始化意图识别机器人 ID
  useIMFetchIntentRecognizeRobotConf();
  // 初始化IM音频配置
  useIMFetchAudioConf();
  // 初始化openInstall
  useInitOpenInstall();
  // 初始化logger
  useInitLogger();
};

const useInitOpenInstall = async () => {
  const agreementState = useAtomValue(agreementStateAtom);

  useEffect(() => {
    const initOpenInstallInfo = async () => {
      if (agreementState === 'agreed') {
        const channel = await ChannelHelper.shared().getChannel();
        if (channel) {
          store.dispatch(updateOpenInstallInfo({ channel }));
        }
      }
    };

    initOpenInstallInfo();
  }, [agreementState]);
};

const useInitLogger = () => {
  const channel = useChannel();
  useEffect(() => {
    if (channel) {
      LoggerHelper.initLogger();
    }
  }, [channel]);
};
