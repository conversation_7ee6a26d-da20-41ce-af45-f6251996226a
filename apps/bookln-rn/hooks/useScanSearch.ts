import { PermissionEnum, PermissionHooks, PermissionPurposeScene } from '@bookln/permission';
import { successVibrate } from '@jgl/biz-func';
import { router, useDidHide, useDidShow, useWindowDimensions } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useDebounceFn } from 'ahooks';
import { Audio } from 'expo-av';
import * as BarcodeScanner from 'expo-barcode-scanner';
import { type BarCodeScanningResult, FlashMode, type Point } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { LayoutAnimation, Platform } from 'react-native';
import {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { useToast } from 'react-native-toast-hybrid';
import * as ScreenOrientation from 'expo-screen-orientation';
import type { CameraScannerRef } from '../components/CameraScanner';
import { stringIsUrl } from '../utils/QRUtil';
import { qrQuery, treatBarCode, treateCrCodeOfUrl } from '../utils/QRUtils';
import { parseCustomQueryItems } from '../utils/WebViewHelper';
import { SoundUrl } from '../utils/constants';
import { routerMap } from '../utils/routerMap';

// 最大扫描框大小
const maxBoxSize = 327;

type CornerPoint = {
  rightTop: { x: number; y: number };
  rightBottom: { x: number; y: number };
  leftBottom: { x: number; y: number };
  leftTop: { x: number; y: number };
};

/**
 * 二维码信息
 */
export type QRCodeInfo = {
  data: string;
  type: string;
  cornerPoint?: CornerPoint;
};

/**
 * 扫码搜索
 */
export const useScanSearch = () => {
  const cameraScannerRef = useRef<CameraScannerRef>(null);

  const toast = useToast();

  const [flashMode, setFlashMode] = useState<FlashMode>(FlashMode.off);

  const [cameraEnabled, setCameraEnabled] = useState(true);

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const [scanningResult, setScanningResult] = useState<BarCodeScanningResult[]>();

  const { width, height } = useWindowDimensions();

  const centerX = useMemo(() => width / 2, [width]);

  const centerY = useMemo(() => height / 2, [height]);

  const boxSize = useMemo(() => {
    return Math.min(maxBoxSize, Math.min(width, height) - 48);
  }, [width, height]);

  // 创建动画值
  const scanLinePosition = useSharedValue(0);

  /**
   * 查询二维码
   */
  const queryQRCode = useCallback(
    async (code: string) => {
      await qrQuery({
        crCode: code,
        source: 'scanQrCode',
        isFromQr: true,
      }).finally(() => {
        toast.hide();
      });
    },
    [toast],
  );

  const { run: runQueryQRCode } = useDebounceFn(queryQRCode, {
    wait: 1000,
  });

  const dealScanResult = useCallback(
    async (result: BarCodeScanningResult) => {
      const { data, type } = result;
      const isBarCode = BarcodeScanner.Constants.BarCodeType.ean13 === type;
      const looksLikeBarCode = !stringIsUrl(data) && (data || '').length === 13;
      if (isBarCode || looksLikeBarCode) {
        // 条形码 相册读取条形码 没有type 故判断位数13且不是url
        await treatBarCode(data);
        toast.hide();
      } else {
        if (stringIsUrl(data)) {
          router.replace(routerMap.WebView, { url: data });
          toast.hide();
          return;
        }
        const isWeChatUrl =
          data?.includes('http://weixin.qq.com/') || data?.includes('https://weixin.qq.com/');
        if (stringIsUrl(data) && !isWeChatUrl) {
          // 其他
          const parameters = parseCustomQueryItems(data);

          if (parameters != null && !Object.prototype.hasOwnProperty.call(parameters, 'crcode')) {
            treateCrCodeOfUrl(data);
            toast.hide();
            return;
          }
        }
        runQueryQRCode(data);
      }
    },
    [runQueryQRCode, toast],
  );

  /**
   * 播放音效
   */
  const playBeepSound = useCallback(async () => {
    try {
      const { sound } = await Audio.Sound.createAsync({
        uri: SoundUrl.SCAN_SUCCESS_SOUND,
      });
      await sound.playAsync();
    } catch (error) {
      console.log('播放音效出错:', error);
    }
  }, []);

  // 处理选择结果
  const handleSelectResult = useCallback(
    async (result: BarCodeScanningResult) => {
      toast.loading('');
      await dealScanResult(result).finally(() => {
        setScanningResult(undefined);
        scanningRef.current = false;
        tempResultRef.current = [];
      });
    },
    [dealScanResult, toast],
  );

  // 修改handleCollectedResults以处理多个结果
  const { run: handleCollectedResults } = useDebounceFn(
    async () => {
      playBeepSound();
      successVibrate();
      let uniqueResults = tempResultRef.current.filter(
        (result, index, self) => index === self.findIndex((t) => t.data === result.data),
      );
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      if (Platform.OS === 'ios') {
        uniqueResults = await Promise.all(
          uniqueResults.map(async (result) => {
            return {
              ...result,
              cornerPoints: await iOSConvertCornerPointsToScreen(result.cornerPoints ?? []),
            };
          }),
        );
      }
      setScanningResult(uniqueResults);
      const [firstResult] = uniqueResults;
      if (uniqueResults.length === 1 && firstResult) {
        setTimeout(() => {
          setScanningResult(undefined);
          handleSelectResult(firstResult);
        }, 500);
      }
    },
    { wait: 200 },
  );

  const tempResultRef = useRef<BarCodeScanningResult[]>([]);

  const scanningRef = useRef(false);

  /**
   * 扫描结果
   */
  const onScanResult = useCallback(
    async (result: BarCodeScanningResult) => {
      if (scanningRef.current) {
        return;
      }
      scanningRef.current = true;
      cameraScannerRef.current?.pausePreview();
      tempResultRef.current.push(result);
      handleCollectedResults();
    },
    [handleCollectedResults],
  );

  const onPressBack = useCallback(() => {
    router.back();
  }, []);

  useDidHide(() => {
    cameraScannerRef.current?.pausePreview();
    setCameraEnabled(false);
  });

  useDidShow(() => {
    cameraScannerRef.current?.resumePreview();
    setCameraEnabled(true);
  });

  /**
   * 打开相册
   */
  const onPressAlbum = useCallback(async () => {
    const isGranted = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });
    if (isGranted) {
      const pickResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        aspect: [4, 3],
        quality: 1,
      });
      const [asset] = pickResult.assets ?? [];
      if (asset) {
        const scanResult = await BarcodeScanner.scanFromURLAsync(asset.uri).catch((err) => {});
        const [firstResult] = scanResult ?? [];
        if (firstResult) {
          toast.loading('');
          await dealScanResult(firstResult);
        } else {
          showToast({
            title: '未识别到二维码',
          });
        }
      }
    }
  }, [checkAndRequestPermission, dealScanResult, toast]);

  /**
   * 打开闪光灯
   */
  const onPressLight = useCallback(() => {
    setFlashMode((prev) => {
      if (prev === FlashMode.off) {
        return FlashMode.torch;
      }
      return FlashMode.off;
    });
  }, []);

  // 扫描线图片高度
  const scanningImgHeight = useMemo(() => {
    return (boxSize * 60) / 327;
  }, [boxSize]);

  // 设置扫描线动画
  useEffect(() => {
    scanLinePosition.value = withRepeat(
      withTiming(1, {
        duration: 3000,
        easing: Easing.linear,
      }),
      -1, // 无限循环
      false, // 不反向
    );
  }, [scanLinePosition]);

  // 扫描线动画样式
  const scanLineStyle = useAnimatedStyle(() => {
    return {
      top: centerY - boxSize / 2 + scanLinePosition.value * (boxSize - scanningImgHeight),
    };
  });

  const onPressCancelReview = useCallback(() => {
    tempResultRef.current = [];
    setScanningResult(undefined);
    scanningRef.current = false;
  }, []);

  /**
   * ios转换角点坐标到屏幕坐标
   */
  const iOSConvertCornerPointsToScreen = useCallback(
    async (cornerPoints: Point[]): Promise<Point[]> => {
      let screenWidth = width;
      let screenHeight = height;

      const orientation = await ScreenOrientation.getOrientationAsync();

      const isLandscape =
        orientation === ScreenOrientation.Orientation.LANDSCAPE_LEFT ||
        orientation === ScreenOrientation.Orientation.LANDSCAPE_RIGHT;

      if (isLandscape) {
        // 根据方向调整屏幕尺寸
        [screenWidth, screenHeight] = [screenHeight, screenWidth];
      }

      return cornerPoints.map((point) => {
        let x = point.y;
        let y = 1 - point.x;

        // 根据设备方向进一步调整
        switch (orientation) {
          case ScreenOrientation.Orientation.PORTRAIT_DOWN:
          case ScreenOrientation.Orientation.PORTRAIT_UP:
            [x, y] = [1 - x, 1 - y];
            break;
          case ScreenOrientation.Orientation.LANDSCAPE_LEFT:
            [x, y] = [y, 1 - x];
            break;
          case ScreenOrientation.Orientation.LANDSCAPE_RIGHT:
            [x, y] = [1 - y, x];
            break;
          default: // portrait
            break;
        }

        // 处理前置摄像头镜像效果

        return {
          x: x * screenWidth,
          y: y * screenHeight,
        };
      }) as Point[];
    },
    [width, height],
  );

  return {
    onScanResult,
    onPressBack,
    onPressAlbum,
    onPressLight,
    flashMode,
    cameraScannerRef,
    centerX,
    centerY,
    boxSize,
    width,
    height,
    cameraEnabled,
    scanLineStyle,
    scanningImgHeight,
    handleSelectResult,
    onPressCancelReview,
    scanningResult,
  };
};
