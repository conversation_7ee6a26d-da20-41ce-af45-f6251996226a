import { useSafeAreaInsets } from '@jgl/biz-func';
import Icon from '@jgl/icon';
import { showToast } from '@jgl/utils';
import { Camera, CameraType, FlashMode } from 'expo-camera';
import * as ImageManipulator from 'expo-image-manipulator';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from 'expo-router';

import { PermissionEnum, PermissionHooks } from '@bookln/permission';
import { JglText, JglTouchable, JglXStack } from '@jgl/ui-v4';
import { isEmpty } from 'lodash';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  type LayoutChangeEvent,
  Platform,
  StyleSheet,
} from 'react-native';
import Canvas from 'react-native-canvas';
import { Image, View } from 'tamagui';
import { CutScene } from '../cutScene/CutScene';
import type { TakePhotoSceneProps } from './TakePhotoScene.type';

export const TakePhotoScene = memo((props: TakePhotoSceneProps) => {
  const { visible, title, scene, needCut, onCommitPhoto } = props;

  const cameraRef = useRef<Camera>(null);

  const [flashMode, setFlashMode] = useState<FlashMode>(FlashMode.off);

  const [mode, setMode] = useState<'page' | 'question'>('page');

  const [ratio, setRatio] = useState<string>('16:9');

  const navigation = useNavigation();

  const [originalPickPhoto, setOriginalPickPhoto] = useState<string>();
  const [takeLoading, setTakeLoading] = useState(false);

  const { bottom } = useSafeAreaInsets();

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const [canvasSize, setCanvasSize] = useState<{
    width: number;
    height: number;
  }>();

  const handleGoBackToTakePhotoScene = useCallback(() => {
    if (title) {
      navigation.setOptions({ title });
    }
    setOriginalPickPhoto(undefined);
  }, [navigation, title]);

  const handleBeforeRemove = useCallback(
    (e: unknown) => {
      const { type } = e.data.action;
      e.preventDefault();
      if (originalPickPhoto && type !== 'POP') {
        handleGoBackToTakePhotoScene();
      } else {
        navigation.dispatch(e.data.action);
      }
    },
    [handleGoBackToTakePhotoScene, navigation, originalPickPhoto],
  );

  useEffect(() => {
    navigation.addListener('beforeRemove', handleBeforeRemove);
    return () => {
      navigation.removeListener('beforeRemove', handleBeforeRemove);
    };
  }, [handleBeforeRemove, navigation]);

  const handleStartCropping = useCallback(
    async (uri: string) => {
      // 压缩图片
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1000 } }], // 限制宽度为1000像素，高度等比缩放
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG },
      );
      if (needCut || mode === 'question') {
        setOriginalPickPhoto(manipulatedImage.uri);
        navigation.setOptions({ title: '裁剪' });
      } else {
        onCommitPhoto?.(manipulatedImage.uri);
      }
    },
    [mode, navigation, needCut, onCommitPhoto],
  );

  /** 相册选取 */
  const handleClickPhotoLibrary = useCallback(async () => {
    const result = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: scene,
    });
    if (result) {
      const pickResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        aspect: [4, 3],
        quality: 0.8,
      });
      const [asset] = pickResult.assets ?? [];
      if (asset) {
        handleStartCropping(asset.uri);
      }
    } else {
      showToast({ title: '没有访问相册的权限' });
    }
  }, [checkAndRequestPermission, handleStartCropping, scene]);

  /** 照相 */
  const handleClickTakePhoto = useCallback(async () => {
    setTakeLoading(true);
    if (takeLoading) {
      return;
    }
    const snapshot = await cameraRef.current
      ?.takePictureAsync({
        quality: 0.8,
      })
      .finally(() => setTakeLoading(false));
    const { uri } = snapshot ?? {};
    if (uri) {
      handleStartCropping(uri);
    }
  }, [handleStartCropping, takeLoading]);

  /** 打开闪光灯 */
  const handleClickLight = useCallback(() => {
    setFlashMode((prev) => {
      if (prev === FlashMode.off) {
        return FlashMode.torch;
      }
      return FlashMode.off;
    });
  }, []);

  const onCameraReady = useCallback(async () => {
    if (Platform.OS === 'android') {
      // 默认比例
      const targetRatio = 16 / 9;
      const supportRatio = await cameraRef.current?.getSupportedRatiosAsync();
      if (!supportRatio) {
        return;
      }
      const result = supportRatio
        .map((item) => {
          const pair = item.split(':');
          const radioValue =
            Number.parseInt(pair[0]) / Number.parseInt(pair[1]);
          return {
            key: item,
            difference: Math.abs(radioValue - targetRatio),
          };
        })
        .sort((a, b) => {
          return a.difference - b.difference;
        });
      setRatio(result[0].key);
    }
  }, []);

  /** 底部操作区域 */
  const renderBottom = useMemo(() => {
    return (
      <View
        className='relative flex w-full flex-row items-center  justify-between bg-black px-[32px] pt-[33px]'
        style={{ paddingBottom: bottom + 33 }}
      >
        <View
          className='my-auto rounded-full'
          onPress={handleClickPhotoLibrary}
        >
          <Image
            source={require('../assets/images/ic_album.png')}
            width={44}
            height={44}
          />
        </View>
        <View className='flex-center relative'>
          <Image
            source={{ uri: Icon.takePhotoReadingTakePhoto }}
            width={64}
            height={64}
            onPress={handleClickTakePhoto}
          />

          {takeLoading ? (
            <ActivityIndicator className='absolute bottom-0 left-0 right-0 top-0' />
          ) : null}
        </View>

        <View className='my-auto rounded-full' onPress={handleClickLight}>
          <Image
            source={
              flashMode === FlashMode.torch
                ? require('../assets/images/ic_light_on.png')
                : require('../assets/images/ic_light_off.png')
            }
            width={44}
            height={44}
          />
        </View>
      </View>
    );
  }, [
    bottom,
    handleClickPhotoLibrary,
    handleClickTakePhoto,
    takeLoading,
    handleClickLight,
    flashMode,
  ]);

  /** 绘制参考线 */
  const handleCanvas = useCallback(
    (canvas: Canvas) => {
      if (!canvas || !canvasSize) return;
      const { width, height } = canvasSize;
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 0.5;
      ctx.beginPath();
      ctx.moveTo(width / 3, 0);
      ctx.lineTo(width / 3, height);
      ctx.stroke();
      ctx.moveTo((width / 3) * 2, 0);
      ctx.lineTo((width / 3) * 2, height);
      ctx.stroke();
      ctx.moveTo(0, height / 3);
      ctx.lineTo(width, height / 3);
      ctx.stroke();
      ctx.moveTo(0, (height / 3) * 2);
      ctx.lineTo(width, (height / 3) * 2);
      ctx.stroke();

      ctx.font = '16px sans-serif';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('平行纸面，文字对齐参考线', width / 2, height / 2 - 12);
      ctx.fillText('请尽量包含题号信息', width / 2, height / 2 + 12);
    },
    [canvasSize],
  );

  const renderCanvas = useMemo(() => {
    if (canvasSize) {
      return <Canvas ref={handleCanvas} style={StyleSheet.absoluteFill} />;
    }
  }, [canvasSize, handleCanvas]);

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setCanvasSize({ width, height });
  }, []);

  const handleClickSwitchMode = useCallback(() => {
    setMode((prev) => (prev === 'page' ? 'question' : 'page'));
  }, []);

  /** 切换模式 */
  const renderSwitchMode = useCallback(() => {
    return (
      <JglTouchable
        pos='absolute'
        alignSelf='center'
        bottom={10}
        zIndex={1000}
        bg='#0000007F'
        flexDirection='row'
        borderRadius={100}
        overflow='hidden'
        onPress={handleClickSwitchMode}
        p={2}
        style={{ minHeight: 0 }}
      >
        <JglXStack
          px={12}
          py={2}
          borderRadius={100}
          bg={mode === 'page' ? '#FFFFFF4D' : 'transparent'}
        >
          <JglText color='white' fontSize={14}>
            整页
          </JglText>
        </JglXStack>
        <JglXStack
          px={12}
          py={2}
          borderRadius={100}
          bg={mode === 'question' ? '#FFFFFF4D' : 'transparent'}
        >
          <JglText color='white' fontSize={14}>
            单题
          </JglText>
        </JglXStack>
      </JglTouchable>
    );
  }, [handleClickSwitchMode, mode]);

  if (!visible) {
    return null;
  } else {
    return (
      <View className='relative flex h-full w-full'>
        <Camera
          ref={cameraRef}
          className='w-full flex-1'
          type={CameraType.back}
          flashMode={flashMode}
          ratio={ratio}
          onCameraReady={onCameraReady}
          onLayout={!canvasSize ? onLayout : undefined}
        >
          {renderCanvas}
          {renderSwitchMode()}
        </Camera>
        {renderBottom}
        <CutScene
          photoLocalPath={originalPickPhoto}
          visible={!isEmpty(originalPickPhoto)}
          onCommitPhoto={onCommitPhoto}
        />
      </View>
    );
  }
});
