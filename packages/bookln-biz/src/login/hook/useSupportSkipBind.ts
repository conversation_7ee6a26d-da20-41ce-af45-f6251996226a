import { useMemo } from 'react';
import { useAppConfig } from '../../config/useAppConfig';

type BindMobil = {
  // [key: string]: {
  //   vid: number;
  //   supportSkip: boolean;
  // };
  supportSkip: boolean;
};

const defaultResult = true; // 默认支持跳过绑定

/**
 * 是否支持跳过绑定
 * 默认支持跳过绑定
 */
export const useSupportSkipBind = () => {
  const loginConfig = useAppConfig('BooklnLoginConfig');

  // const mockData = {"result": {"bindMobil": { "huawei": { "vid": 307, "supportSkip": false } } } };

  const supportSkipBind = useMemo(() => {
    const bindMobil = JSON.parse(loginConfig ?? '{}').bindMobile as BindMobil;
    const { supportSkip = defaultResult } = bindMobil ?? {};
    return supportSkip;
  }, [loginConfig]);

  return supportSkipBind;
};
