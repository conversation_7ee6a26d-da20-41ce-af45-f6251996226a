import {
  agreementStateAtom,
  isLoggingOutAtom,
  updateUserInfo,
  useAppDispatch,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { storage, USERINFO } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useSetAtom } from 'jotai';
import { useCallback } from 'react';
import { logout } from '../api/UserServiceApi';

/**
 * 退出登录
 * @returns
 */
export const useLogout = () => {
  const setIsLoggingOut = useSetAtom(isLoggingOutAtom);

  const setAgreementState = useSetAtom(agreementStateAtom);

  const dispatch = useAppDispatch();

  /**
   * 退出登录
   * 先退出登录再使用 uuid 登录
   */
  const handleLogout = useCallback(async (): Promise<boolean> => {
    setIsLoggingOut(true);

    const { success, msg, data } = await container
      .net()
      .fetch(logout())
      .finally(() => {
        setIsLoggingOut(false);
      });
    if (success && data?.result === 'true') {
      // 清除tid
      await storage.removeItem(USERINFO);
      dispatch(updateUserInfo({}));
      setAgreementState('undetermined');
      return true;
    } else {
      showToast({ title: msg ?? '请求数据失败' });
    }
    return false;
  }, [dispatch, setAgreementState, setIsLoggingOut]);

  return {
    logout: handleLogout,
  };
};
