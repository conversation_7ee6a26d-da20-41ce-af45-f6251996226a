import { agreementLinks, openExternalLink } from '@jgl/biz-func';
import { JglText, JglTouchable, JglXStack } from '@jgl/ui-v4';
import { Checkbox, Image } from 'tamagui';

type Props = { textClassName?: string };

const checkboxHitSlop = { bottom: 16, top: 16, left: 16, right: 16 };

type LoginAgreementProps = {
  isChecked: boolean;
  onCheckedChange: (checked?: boolean) => void;
} & Props;

export const LoginAgreement = (props: LoginAgreementProps) => {
  const { textClassName, isChecked, onCheckedChange } = props;

  return (
    <JglXStack space={'$2'} jglClassName={textClassName}>
      <Checkbox
        hitSlop={checkboxHitSlop}
        borderRadius={1000}
        w={14}
        h={14}
        borderColor={'#D0D1D7'}
        checked={isChecked}
        onCheckedChange={(checked) => {
          if (checked === 'indeterminate') {
            onCheckedChange(false);
          } else {
            onCheckedChange(checked);
          }
        }}
      >
        <Checkbox.Indicator>
          <Image
            source={require('../../assets/images/ic_checked.png')}
            width={16}
            height={16}
          />
        </Checkbox.Indicator>
      </Checkbox>
      <JglText jglClassName='flex-1 text-sm'>
        <JglTouchable onPress={onCheckedChange} minH={0} minW={0}>
          <JglText color='#8C8C8C' fontSize={12} fontWeight='bold'>
            我已阅读并同意
          </JglText>
        </JglTouchable>
        {agreementLinks.map((link) => {
          const { name, url } = link;
          return (
            <JglTouchable
              key={name}
              onPress={() => openExternalLink(url)}
              px={2}
              minH={0}
              minW={0}
            >
              <JglText fontSize={12} fontWeight='bold' color='#4E76FF'>
                《{name}》
              </JglText>
            </JglTouchable>
          );
        })}
      </JglText>
    </JglXStack>
  );
};
