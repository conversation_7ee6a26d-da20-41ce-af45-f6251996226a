import {
  JglGameButton,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useCallback } from 'react';
import { ActivityIndicator } from 'react-native';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Image } from 'tamagui';
import { useLogin } from '../hook/useLogin';
import { LoginAgreement } from './components/LoginAgreement';
import type { MainContentProps } from '../type/MainContent.type';

export const LoginContent: React.FC<MainContentProps> = ({
  onPressLoginByMobile,
}) => {
  const {
    onPressLoginByWeChat,
    isLoggingInByWeChat,
    onCheckedChange,
    isChecked,
    onPressBrowseMode,
    isShowBrowseModeButton,
    isShowWechatRecommendTag,
  } = useLogin();

  const renderOtherWay = useCallback(() => {
    return (
      <SafeAreaView className='w-full'>
        <JglYStack mb={16}>
          <JglXStack alignItems='center' justifyContent='center'>
            <LinearGradient
              className='h-px w-12'
              start={{ x: 1, y: 0 }}
              end={{ x: 0, y: 0 }}
              colors={['#E2E2E2', '#E2E2E257']}
            />
            <JglText
              fontSize={14}
              color={'#6F6F6F'}
              marginLeft={8}
              marginRight={8}
            >
              其他方式
            </JglText>
            <LinearGradient
              className='h-px w-12'
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#E2E2E2', '#E2E2E257']}
            />
          </JglXStack>
          <JglXStack justifyContent='space-evenly'>
            <JglTouchable
              onPress={onPressLoginByMobile}
              mt={12}
              alignItems='center'
            >
              <JglYStack alignItems='center'>
                <Image
                  source={require('../assets/images/ic_phone_circle.png')}
                  width={46}
                  height={46}
                />
                <JglText fontSize={12} color='#343434'>
                  手机号登录
                </JglText>
              </JglYStack>
            </JglTouchable>
            {isShowBrowseModeButton && (
              <JglTouchable
                onPress={onPressBrowseMode}
                mt={12}
                alignItems='center'
              >
                <JglYStack alignItems='center'>
                  <Image
                    source={require('../assets/images/ic_scan_mode.png')}
                    width={46}
                    height={46}
                  />
                  <JglText fontSize={12} color='#343434'>
                    浏览模式
                  </JglText>
                </JglYStack>
              </JglTouchable>
            )}
          </JglXStack>
        </JglYStack>
      </SafeAreaView>
    );
  }, [onPressLoginByMobile, onPressBrowseMode, isShowBrowseModeButton]);

  const renderWechatRecommendTag = useCallback(() => {
    if (!isShowWechatRecommendTag) {
      return null;
    }
    return (
      <JglXStack
        position='absolute'
        top={'-90%'}
        bg='#FFA01C'
        left={'95%'}
        px={8}
        py={2}
        borderTopRightRadius={10}
        borderTopLeftRadius={10}
        borderBottomRightRadius={10}
        zIndex={1000}
      >
        <JglText fontSize={12} color='white' fontWeight='bold'>
          推荐
        </JglText>
      </JglXStack>
    );
  }, [isShowWechatRecommendTag]);

  return (
    <JglYStack bg='white' flex={1} position='relative'>
      <JglYStack jglClassName='flex-center flex-1' bg='white' flex={1}>
        <Image source={require('../assets/images/ic_log_in_logo.png')} />
      </JglYStack>
      <JglYStack jglClassName='items-center flex-1 justify-between bg-white pb-4'>
        <JglYStack w={'80%'} maxW={384} space='$3' alignItems='stretch'>
          <JglGameButton
            onPress={onPressLoginByWeChat}
            size='large'
            mb={32}
            backgroundColor='#4E76FF'
            secondaryBgColor='#2E58E9'
            overflowY='visible'
            style={{ overflow: 'visible' }}
            radius={10}
          >
            <JglXStack
              minH={44}
              jglClassName='flex-center'
              py={10}
              zIndex={1000}
            >
              {isLoggingInByWeChat ? (
                <ActivityIndicator color={'white'} />
              ) : (
                <JglXStack spaceX={8} alignItems='center' position='relative'>
                  <FontAwesome name='wechat' size={20} color={'white'} />
                  <JglText fontSize={16} color={'white'} fontWeight='bold'>
                    微信登录
                  </JglText>
                  {renderWechatRecommendTag()}
                </JglXStack>
              )}
            </JglXStack>
          </JglGameButton>

          <LoginAgreement
            isChecked={isChecked}
            onCheckedChange={onCheckedChange}
          />
        </JglYStack>
        {renderOtherWay()}
      </JglYStack>
    </JglYStack>
  );
};
